/**
 * @file api_router.h
 * @brief API路由系统 - libmicrohttpd HTTP服务器接口
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef API_ROUTER_H
#define API_ROUTER_H

#include <microhttpd.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 最大路由数量
#define MAX_API_ROUTES 100

// 最大POST数据大小
#define MAX_POST_DATA_SIZE 65536

// 连接状态结构 - 用于处理POST数据
typedef struct {
    char *post_data;
    size_t post_data_size;
    size_t post_data_capacity;
    int first_call;
} connection_state_t;

// HTTP方法枚举
typedef enum {
    HTTP_METHOD_GET = 0,
    HTTP_METHOD_POST,
    HTTP_METHOD_PUT,
    HTTP_METHOD_DELETE,
    HTTP_METHOD_PATCH,
    HTTP_METHOD_HEAD,
    HTTP_METHOD_OPTIONS,
    HTTP_METHOD_MAX
} http_method_t;

// API响应结构
typedef struct {
    int status_code;
    char *content_type;
    char *response_data;
    size_t data_length;
} api_response_t;

// HTTP服务器配置
typedef struct {
    int port;
    int max_connections;
    int timeout_seconds;
    const char *document_root;
} http_server_config_t;

// API处理函数类型
typedef int (*api_handler_func_t)(struct MHD_Connection *connection, 
                                  const char *url, 
                                  const char *method,
                                  const char *upload_data, 
                                  size_t *upload_data_size,
                                  void **con_cls);

// API路由结构
typedef struct {
    char *path;
    http_method_t method;
    api_handler_func_t handler;
    char *description;
} api_route_t;

/**
 * @brief 初始化API路由系统
 * @return 0成功，-1失败
 */
int api_router_init(void);

/**
 * @brief 清理API路由系统
 */
void api_router_cleanup(void);

/**
 * @brief 注册API路由
 * @param path URL路径
 * @param method HTTP方法
 * @param handler 处理函数
 * @param description 描述信息
 * @return 0成功，-1失败
 */
int api_router_register(const char *path, http_method_t method, 
                       api_handler_func_t handler, const char *description);

/**
 * @brief 查找API路由
 * @param path URL路径
 * @param method HTTP方法
 * @return 路由指针，NULL表示未找到
 */
api_route_t *api_router_find(const char *path, http_method_t method);

/**
 * @brief 启动HTTP服务器
 * @param config 服务器配置
 * @return 0成功，-1失败
 */
int http_server_start(const http_server_config_t *config);

/**
 * @brief 停止HTTP服务器
 */
void http_server_stop(void);

/**
 * @brief 创建JSON响应
 * @param status_code HTTP状态码
 * @param json_data JSON数据
 * @return API响应结构指针
 */
api_response_t *api_response_create_json(int status_code, const char *json_data);

/**
 * @brief 创建错误响应
 * @param status_code HTTP状态码
 * @param error_message 错误消息
 * @return API响应结构指针
 */
api_response_t *api_response_create_error(int status_code, const char *error_message);

/**
 * @brief 释放API响应
 * @param response 响应结构指针
 */
void api_response_free(api_response_t *response);

/**
 * @brief HTTP方法字符串转枚举
 * @param method_str HTTP方法字符串
 * @return HTTP方法枚举
 */
http_method_t http_method_from_string(const char *method_str);

/**
 * @brief HTTP方法枚举转字符串
 * @param method HTTP方法枚举
 * @return HTTP方法字符串
 */
const char *http_method_to_string(http_method_t method);

#ifdef __cplusplus
}
#endif

#endif // API_ROUTER_H
