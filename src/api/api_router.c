/**
 * @file api_router.c
 * @brief API路由系统实现 - libmicrohttpd HTTP服务器
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <cJSON.h>

// 全局变量
static api_route_t g_api_routes[MAX_API_ROUTES];
static int g_route_count = 0;
static struct MHD_Daemon *g_http_daemon = NULL;

// HTTP方法字符串映射
static const char *g_http_method_strings[] = {
    "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"
};

/**
 * @brief HTTP请求处理函数
 */
static enum MHD_Result handle_http_request(void *cls,
                                          struct MHD_Connection *connection,
                                          const char *url,
                                          const char *method,
                                          const char *version,
                                          const char *upload_data,
                                          size_t *upload_data_size,
                                          void **con_cls);

/**
 * @brief 发送HTTP响应
 */
static enum MHD_Result send_response(struct MHD_Connection *connection,
                                    const api_response_t *response);

/**
 * @brief 连接清理回调函数
 */
static void connection_cleanup(void *cls, struct MHD_Connection *connection,
                              void **con_cls, enum MHD_RequestTerminationCode toe);

int api_router_init(void) {
    memset(g_api_routes, 0, sizeof(g_api_routes));
    g_route_count = 0;
    g_http_daemon = NULL;
    return 0;
}

void api_router_cleanup(void) {
    // 清理路由表
    for (int i = 0; i < g_route_count; i++) {
        if (g_api_routes[i].path) {
            free(g_api_routes[i].path);
        }
        if (g_api_routes[i].description) {
            free(g_api_routes[i].description);
        }
    }
    memset(g_api_routes, 0, sizeof(g_api_routes));
    g_route_count = 0;
}

int api_router_register(const char *path, http_method_t method, 
                       api_handler_func_t handler, const char *description) {
    if (!path || !handler || g_route_count >= MAX_API_ROUTES) {
        return -1;
    }

    api_route_t *route = &g_api_routes[g_route_count];
    route->path = strdup(path);
    route->method = method;
    route->handler = handler;
    route->description = description ? strdup(description) : NULL;

    g_route_count++;
    return 0;
}

api_route_t *api_router_find(const char *path, http_method_t method) {
    if (!path) {
        return NULL;
    }

    for (int i = 0; i < g_route_count; i++) {
        if (g_api_routes[i].method == method && 
            strcmp(g_api_routes[i].path, path) == 0) {
            return &g_api_routes[i];
        }
    }
    return NULL;
}

http_method_t http_method_from_string(const char *method_str) {
    if (!method_str) {
        return HTTP_METHOD_MAX;
    }

    for (int i = 0; i < HTTP_METHOD_MAX; i++) {
        if (strcmp(method_str, g_http_method_strings[i]) == 0) {
            return (http_method_t)i;
        }
    }
    return HTTP_METHOD_MAX;
}

const char *http_method_to_string(http_method_t method) {
    if (method >= HTTP_METHOD_MAX) {
        return "UNKNOWN";
    }
    return g_http_method_strings[method];
}

int http_server_start(const http_server_config_t *config) {
    if (!config || g_http_daemon) {
        return -1;
    }

    g_http_daemon = MHD_start_daemon(
        MHD_USE_INTERNAL_POLLING_THREAD,
        config->port,
        NULL, NULL,
        &handle_http_request, NULL,
        MHD_OPTION_NOTIFY_COMPLETED, &connection_cleanup, NULL,
        MHD_OPTION_CONNECTION_LIMIT, config->max_connections,
        MHD_OPTION_CONNECTION_TIMEOUT, config->timeout_seconds,
        MHD_OPTION_END
    );

    return g_http_daemon ? 0 : -1;
}

void http_server_stop(void) {
    if (g_http_daemon) {
        MHD_stop_daemon(g_http_daemon);
        g_http_daemon = NULL;
    }
}

api_response_t *api_response_create_json(int status_code, const char *json_data) {
    api_response_t *response = malloc(sizeof(api_response_t));
    if (!response) {
        return NULL;
    }

    response->status_code = status_code;
    response->content_type = strdup("application/json");
    response->response_data = json_data ? strdup(json_data) : NULL;
    response->data_length = response->response_data ? strlen(response->response_data) : 0;

    return response;
}

api_response_t *api_response_create_error(int status_code, const char *error_message) {
    cJSON *json = cJSON_CreateObject();
    cJSON *error = cJSON_CreateString(error_message ? error_message : "Unknown error");
    cJSON_AddItemToObject(json, "error", error);

    char *json_string = cJSON_Print(json);
    api_response_t *response = api_response_create_json(status_code, json_string);

    free(json_string);
    cJSON_Delete(json);
    return response;
}

void api_response_free(api_response_t *response) {
    if (response) {
        if (response->content_type) {
            free(response->content_type);
        }
        if (response->response_data) {
            free(response->response_data);
        }
        free(response);
    }
}

static enum MHD_Result handle_http_request(void *cls,
                                          struct MHD_Connection *connection,
                                          const char *url,
                                          const char *method,
                                          const char *version,
                                          const char *upload_data,
                                          size_t *upload_data_size,
                                          void **con_cls) {
    (void)cls;
    (void)version;

    // 查找路由
    http_method_t http_method = http_method_from_string(method);
    api_route_t *route = api_router_find(url, http_method);

    if (!route) {
        // 404 Not Found
        api_response_t *response = api_response_create_error(404, "API endpoint not found");
        enum MHD_Result result = send_response(connection, response);
        api_response_free(response);
        return result;
    }

    // 处理POST数据接收
    if (http_method == HTTP_METHOD_POST) {
        connection_state_t *conn_state = (connection_state_t *)*con_cls;

        if (conn_state == NULL) {
            // 第一次调用，初始化连接状态
            conn_state = malloc(sizeof(connection_state_t));
            if (!conn_state) {
                return MHD_NO;
            }
            conn_state->post_data = NULL;
            conn_state->post_data_size = 0;
            conn_state->post_data_capacity = 0;
            conn_state->first_call = 1;
            *con_cls = conn_state;
            return MHD_YES;
        }

        if (*upload_data_size != 0) {
            // 接收POST数据
            size_t new_size = conn_state->post_data_size + *upload_data_size;
            if (new_size > MAX_POST_DATA_SIZE) {
                // 数据太大
                return MHD_NO;
            }

            if (new_size > conn_state->post_data_capacity) {
                // 扩展缓冲区
                size_t new_capacity = (new_size + 1023) & ~1023; // 1KB对齐
                char *new_buffer = realloc(conn_state->post_data, new_capacity);
                if (!new_buffer) {
                    return MHD_NO;
                }
                conn_state->post_data = new_buffer;
                conn_state->post_data_capacity = new_capacity;
            }

            // 复制数据
            memcpy(conn_state->post_data + conn_state->post_data_size, upload_data, *upload_data_size);
            conn_state->post_data_size += *upload_data_size;
            *upload_data_size = 0;
            return MHD_YES;
        } else {
            // 数据接收完成，添加字符串结束符
            if (conn_state->post_data) {
                if (conn_state->post_data_size >= conn_state->post_data_capacity) {
                    char *new_buffer = realloc(conn_state->post_data, conn_state->post_data_size + 1);
                    if (!new_buffer) {
                        return MHD_NO;
                    }
                    conn_state->post_data = new_buffer;
                }
                conn_state->post_data[conn_state->post_data_size] = '\0';
            }
        }
    }

    // 调用处理函数
    int handler_result = route->handler(connection, url, method, upload_data, upload_data_size, con_cls);

    return (handler_result == 0) ? MHD_YES : MHD_NO;
}

static enum MHD_Result send_response(struct MHD_Connection *connection,
                                    const api_response_t *response) {
    if (!response) {
        return MHD_NO;
    }

    struct MHD_Response *mhd_response = MHD_create_response_from_buffer(
        response->data_length,
        (void*)response->response_data,
        MHD_RESPMEM_MUST_COPY
    );

    if (!mhd_response) {
        return MHD_NO;
    }

    // 设置Content-Type
    if (response->content_type) {
        MHD_add_response_header(mhd_response, "Content-Type", response->content_type);
    }

    // 添加CORS头
    MHD_add_response_header(mhd_response, "Access-Control-Allow-Origin", "*");
    MHD_add_response_header(mhd_response, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    MHD_add_response_header(mhd_response, "Access-Control-Allow-Headers", "Content-Type, Authorization");

    enum MHD_Result result = MHD_queue_response(connection, response->status_code, mhd_response);
    MHD_destroy_response(mhd_response);

    return result;
}

static void connection_cleanup(void *cls, struct MHD_Connection *connection,
                              void **con_cls, enum MHD_RequestTerminationCode toe) {
    (void)cls;
    (void)connection;
    (void)toe;

    if (con_cls && *con_cls) {
        connection_state_t *conn_state = (connection_state_t *)*con_cls;
        if (conn_state->post_data) {
            free(conn_state->post_data);
        }
        free(conn_state);
        *con_cls = NULL;
    }
}
